package main

import (
	"encoding/json"
	"fmt"
	"jackpot/api/elixir/unseal"
	"jackpot/filelog"
	"os"
)

func main() {
	// 创建测试用的配置文件
	createTestConfig()

	// 创建处理器
	handler := unseal.NewHandler()

	// 创建日志器
	logger, err := filelog.NewFileLogger("test.log", 1024*1024, 5)
	if err != nil {
		fmt.Printf("创建日志器失败: %v\n", err)
		return
	}

	// 初始化处理器
	err = handler.Init(logger, nil)
	if err != nil {
		fmt.Printf("初始化处理器失败: %v\n", err)
		return
	}

	// 测试场景：开奖是 [0,1,2,3]，但是 countList是 [1, 0, 1, 1, 1, 1, 1, 1]
	// 期望：即使位置1的countList为0，但开奖中存在仙丹1，也应该返回
	req := unseal.Request{
		Oid:    "test-batch-001",
		UserId: "test-user-001",
		Map:    100, // 火焰山
		Counts: []int64{1, 0, 1, 1, 1, 1, 1, 1}, // 位置1为0
	}

	// 验证请求
	err = handler.Validate(req)
	if err != nil {
		fmt.Printf("请求验证失败: %v\n", err)
		return
	}

	// 处理请求
	jackpot, response, err := handler.Handle(req)
	if err != nil {
		fmt.Printf("处理请求失败: %v\n", err)
		return
	}

	fmt.Printf("剩余奖池: %d\n", jackpot)
	fmt.Printf("响应: %+v\n", response)

	// 解析响应数据
	if responseData, ok := response.Data.(*unseal.Response); ok {
		fmt.Printf("开奖结果:\n")
		fmt.Printf("选中的仙丹: %+v\n", responseData.Elixirs)
		fmt.Printf("物品列表:\n")
		for i, item := range responseData.Items {
			if item.Value > 0 || item.Name != "" {
				fmt.Printf("  位置 %d (仙丹 %d): %s (价值:%d, 数量:%d, 总数:%d)\n", 
					i, i%8, item.Name, item.Value, item.Count, item.Total)
			} else {
				fmt.Printf("  位置 %d (仙丹 %d): 空\n", i, i%8)
			}
		}

		// 检查关键测试点：位置1（仙丹1）是否有物品
		if len(responseData.Items) > 1 {
			item1 := responseData.Items[1]
			if item1.Name != "" && item1.Value > 0 {
				fmt.Printf("\n✅ 测试通过：位置1（countList=0）有物品返回: %s\n", item1.Name)
			} else {
				fmt.Printf("\n❌ 测试失败：位置1（countList=0）没有物品返回\n")
			}
		}
	}

	// 清理测试文件
	os.Remove("elixir_unseal.json")
	os.Remove("test.log")
}

func createTestConfig() {
	config := map[string]map[string][]map[string]interface{}{
		"100": { // 火焰山
			"0": []map[string]interface{}{
				{"id": 1001, "name": "铜币", "value": 10, "count": 1},
				{"id": 1002, "name": "银币", "value": 20, "count": 1},
			},
			"1": []map[string]interface{}{
				{"id": 2001, "name": "金币", "value": 100, "count": 1},
				{"id": 2002, "name": "宝石", "value": 200, "count": 1},
			},
			"2": []map[string]interface{}{
				{"id": 3001, "name": "装备", "value": 500, "count": 1},
				{"id": 3002, "name": "武器", "value": 800, "count": 1},
			},
			"3": []map[string]interface{}{
				{"id": 4001, "name": "神器", "value": 1500, "count": 1},
				{"id": 4002, "name": "传说", "value": 2000, "count": 1},
			},
			"4": []map[string]interface{}{
				{"id": 5001, "name": "史诗", "value": 5000, "count": 1},
				{"id": 5002, "name": "神话", "value": 10000, "count": 1},
			},
		},
	}

	data, _ := json.MarshalIndent(config, "", "  ")
	os.WriteFile("elixir_unseal.json", data, 0644)
}
