package reroll

import (
	"encoding/json"
	"fmt"
	"jackpot/api/core"
	"jackpot/filelog"
	"jackpot/items"
	"jackpot/net"
	"jackpot/users"
	"os"
	"slices"
)

// Request 请求体
type Request struct {
	Oid       string  `json:"oid"`       // 批次ID
	UserId    string  `json:"uid"`       // 用户ID
	Map       int64   `json:"map"`       // 100-火焰山，1000-盘丝洞，10000-狮驼岭
	Counts    []int64 `json:"counts"`    // 每种物品的数量，全地图时长度为24，否则为8
	ReJackpot int64   `json:"rejackpot"` // 重填奖池
}

// Response 响应数据
type Response struct {
	Request
	Items []items.Item `json:"items"`
}

// Handler 处理器
type Handler struct {
	logger  core.Logger
	itemMap map[int64][]items.Item
	giftMap map[int][]items.Item
}

// NewHandler 创建处理器
func NewHandler() *Handler {
	return &Handler{
		itemMap: make(map[int64][]items.Item),
		giftMap: make(map[int][]items.Item),
	}
}

// Init 初始化处理器
func (h *Handler) Init(logger *filelog.FileLogger, config any) error {
	h.logger = core.NewLoggerAdapter(logger)

	if err := h.readItemJson(); err != nil {
		return err
	}

	if err := h.readGiftJson(); err != nil {
		return err
	}

	h.logger.Debug("exchange", "giftMap: %+v", h.giftMap)
	return nil
}

// readGiftJson 读取礼物配置
func (h *Handler) readGiftJson() error {
	jsonData, err := os.ReadFile("exchange.json")
	if err != nil {
		return err
	}

	var itemMapTemp = make(map[string][]items.ItemConfig)
	err = json.Unmarshal(jsonData, &itemMapTemp)
	if err != nil {
		return err
	}

	for k, v := range itemMapTemp {
		var key int
		fmt.Sscanf(k, "%d", &key)
		var itemList []items.Item
		for _, itemConfig := range v {
			itemList = append(itemList, items.Item(itemConfig))
		}
		h.giftMap[key] = itemList
	}
	return nil
}

// readItemJson 读取物品配置
func (h *Handler) readItemJson() error {
	jsonData, err := os.ReadFile("monster.json")
	if err != nil {
		return err
	}

	var itemMapTemp = make(map[string][]items.ItemConfig)
	err = json.Unmarshal(jsonData, &itemMapTemp)
	if err != nil {
		return err
	}

	for k, v := range itemMapTemp {
		var key int64
		fmt.Sscanf(k, "%d", &key)
		var itemList []items.Item
		for _, itemConfig := range v {
			itemList = append(itemList, items.Item(itemConfig))
		}
		h.itemMap[key] = itemList
	}

	return nil
}

// Validate 验证请求
func (h *Handler) Validate(req Request) error {
	if len(req.UserId) == 0 {
		return fmt.Errorf("无效用户参数")
	}

	switch req.Map {
	case net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000:
	default:
		return fmt.Errorf("无效地图参数")
	}

	if len(req.Counts) != 8 {
		return fmt.Errorf("无效或缺少counts参数")
	}

	if req.ReJackpot <= 0 {
		return fmt.Errorf("无效或缺少rejackpot参数")
	}

	return nil
}

// Handle 处理请求
func (h *Handler) Handle(req Request) (int64, net.Response, error) {
	jackpot, itemList := h.open(req.Map, req.Counts, req.ReJackpot)
	response := net.Response{
		Code: 0,
		Msg:  "",
		Data: &Response{
			Request: req,
			Items:   itemList,
		},
	}

	return jackpot, response, nil
}

// open 开奖逻辑
func (h *Handler) open(mapId int64, countList []int64, rejackpot int64) (int64, []items.Item) {
	itemList := h.itemMap[mapId]
	result := make([]items.Item, len(itemList))

	global := users.GetGlobalStats(mapId, users.VER_V2)
	global.TotalReturn -= rejackpot
	global.TotalJackpot += rejackpot

	h.logger.Debug("exchange", "[0] global:%+v", global)

	totalReturn := int64(0)
	totalJackpot := global.TotalJackpot

	for i := len(itemList) - 1; i >= 0; i-- {
		item := itemList[i]
		count := countList[i]

		giftList := append(make([]items.Item, 0, len(h.giftMap[item.ID])), h.giftMap[item.ID]...)

		for j := len(giftList) - 1; j >= 0; j-- {
			gift := giftList[j]
			giftCost := gift.Value * gift.Count * count
			if count > 0 && giftCost > totalJackpot && len(giftList) > 1 {
				giftList = slices.Delete(giftList, j, j+1)
			}
		}

		giftList = items.Normalize(giftList)

		selectedItem := items.SelectItemSortByValue(giftList)
		selectedItem.Total = count

		total := selectedItem.Value * selectedItem.Count * selectedItem.Total
		totalReturn += total
		totalJackpot -= total

		result[i] = selectedItem
		h.logger.Debug("exchange", "[10] return item:%s, value:%d, count:%d, total:%d", selectedItem.Name, selectedItem.Value, selectedItem.Count, selectedItem.Total)
	}

	global.TotalReturn += totalReturn
	global.TotalJackpot -= totalReturn
	users.UpdateGobalStats(global)

	return global.TotalJackpot, result
}

// NewModule 创建模块
func NewModule() core.APIModule {
	handler := NewHandler()

	return core.NewModuleBuilder[Request, net.Response]("reroll", "v1", net.MSG_TYPE_OPEN_REROLL).
		WithHandler(handler).
		Build()
}
