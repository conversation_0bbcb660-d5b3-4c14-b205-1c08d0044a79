package items

import (
	"encoding/json"
	"jackpot/filelog"
	"jackpot/users"
	"math/rand/v2"
	"os"
	"sort"
)

type ItemBase struct {
	ID    int    `json:"id"`
	Name  string `json:"name"`
	Value int64  `json:"value"`
}

type ItemConfig struct {
	ID          int     `json:"id"`
	Name        string  `json:"name"`
	Value       int64   `json:"value"`
	Count       int64   `json:"count"`
	Total       int64   `json:"total"`
	Probability float64 `json:"prob"`
}

type Item struct {
	ID          int     `json:"id"`
	Name        string  `json:"name"`
	Value       int64   `json:"value"`
	Count       int64   `json:"count"`
	Total       int64   `json:"total"`
	Probability float64 `json:"-"`
}

type GiftConfig struct {
	ID          int     `json:"giftId"`
	Name        string  `json:"giftName"`
	Count       int64   `json:"giftCount"`
	Value       int64   `json:"giftValue"`
	Probability float64 `json:"giftProb"`
}

const (
	ID_NEIDAN      = 2991030 // 内丹
	ID_BAJIAOSHAN  = 2991024 // 芭蕉扇
	ID_ZHAOYAOJING = 2991025 // 照妖镜
	ID_JINGANGZHUO = 2991026 // 金刚琢
)

var (
	logger  *filelog.FileLogger
	itemMap map[int]ItemBase
)

func InitItems(l *filelog.FileLogger) error {
	// 读取文件内容
	jsonData, err := os.ReadFile("items.json")
	if err != nil {
		return err
	}

	var itemList []ItemBase
	// 解析JSON数据到结构体
	err = json.Unmarshal(jsonData, &itemList)
	if err != nil {
		return err
	}

	logger = l
	logger.Debug("main", "%+v", itemList)

	itemMap = make(map[int]ItemBase)
	for _, item := range itemList {
		itemMap[item.ID] = item
	}

	return nil
}

// FilterItemsByEv 根据EV值筛选出物品列表
func FilterItemsByEv(stats *users.Stats, global *users.GlobalStats, items []Item) []Item {
	minEv := float64(stats.FixedCost) * 0
	maxEv := float64(stats.TotalCost) * stats.Rtp

	jackpot := global.TotalJackpot

	var filteredItems []Item
	var lastItem *Item
	for _, item := range items {
		if item.Probability >= 1 {
			filteredItems = append(filteredItems, item)
			continue
		}
		v := item.Value * item.Count
		if v <= int64(minEv) {
			lastItem = &item
		}
		if v > int64(maxEv) {
			break
		}
		if v > jackpot {
			break
		}
		if v >= int64(minEv) {
			filteredItems = append(filteredItems, item)
		}
	}

	logger.Debug(stats.UserId, "[1] min ev:%f, max ev:%f, multi:%d, repeat:%d, jackpot:%d", minEv, maxEv, stats.FixedMulti, stats.FixedRepeat, jackpot)

	if len(filteredItems) == 0 {
		if lastItem != nil {
			logger.Debug(stats.UserId, "[1] use last item %v", *lastItem)
			filteredItems = append(filteredItems, *lastItem)
		} else {
			logger.Warn(stats.UserId, "[1] no items !!!")
			filteredItems = append(filteredItems, items[0])
		}
	}

	return filteredItems
}

// Normalize 归一化
func Normalize(items []Item) []Item {
	sumProbability := 0.0
	for _, item := range items {
		sumProbability += item.Probability
	}
	if sumProbability > 1 {
		sumProbability = 1
	}

	var normalizedItems []Item
	remainingProbability := 1.0 // 归一化后总概率为1

	for i := 1; i < len(items); i++ {
		var item = items[i]
		item.Probability /= sumProbability
		remainingProbability -= item.Probability
		normalizedItems = append(normalizedItems, item)
	}

	if remainingProbability > 0 {
		var item = items[0]
		item.Probability = remainingProbability
		normalizedItems = append(normalizedItems, item)
	}

	return normalizedItems
}

// SelectItemSortByValue 根据概率分布选择物品
func SelectItemSortByValue(items []Item) Item {
	var selectedItem Item

	// 先根据物品价值降序排序以便更有利返回对用户收益最高的物品
	sort.Slice(items, func(i, j int) bool {
		return items[i].Value*items[i].Count > items[j].Value*items[j].Count
	})

	r := rand.Float64()
	cumulativeProbability := 0.0
	for _, item := range items {
		cumulativeProbability += item.Probability
		if r < cumulativeProbability {
			selectedItem = item
			break // 选择一个物品后即跳出循环，开始下一次判断
		}
	}

	logger.Debug("items", "[6] select item name:%s, value:%d, rand:%f", selectedItem.Name, selectedItem.Value*selectedItem.Count, r)
	return selectedItem
}

func SelectItem(uid string, items []Item) Item {
	var selectedItem Item

	// 根据物品概率升序排序
	sort.Slice(items, func(i, j int) bool {
		return items[i].Probability < items[j].Probability
	})

	r := rand.Float64()
	p := 0.0
	for _, item := range items {
		p += item.Probability
		if r < p {
			selectedItem = item
			break
		}
	}

	logger.Debug(uid, "[6] select item name:%s, value:%d, rand:%f", selectedItem.Name, selectedItem.Value*selectedItem.Count, r)
	return selectedItem
}

func GenerateItem(id int, count int64, total int64) Item {
	itemBase := itemMap[id]
	return Item{
		ID:    itemBase.ID,
		Name:  itemBase.Name,
		Value: itemBase.Value,
		Count: count,
		Total: total,
	}
}
