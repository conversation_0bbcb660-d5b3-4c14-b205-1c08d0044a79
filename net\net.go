package net

type RequestChannel struct {
	Code    byte
	Data    []byte
	Jackpot int64
}

type Response struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data any    `json:"data,omitempty"`
}

const (
	MSG_STATE_OK              byte = 0  //消息正常
	MSG_STATE_TIMEOUT         byte = 1  //消息超时
	MSG_STATE_ERROR_JSON      byte = 2  //消息结构解析错误
	MSG_STATE_ERROR_LOGIC     byte = 3  //消息逻辑执行错误
	MSG_STATE_ERROR_UNDIFINE  byte = 4  //消息不存在错误
	MSG_STATE_ERROR_DUPLICATE byte = 5  //重复协议标识
	MSG_STATE_ERROR_HEART     byte = 6  //心跳包
	MSG_TYPE_OPEN_COMMON      byte = 1  //探索普通怪
	MSG_TYPE_OPEN_CHAMPION    byte = 2  //探索精英怪
	MSG_TYPE_OPEN_BOSS        byte = 3  //探索世界BOSS
	MSG_TYPE_OPEN_PUT         byte = 4  //补充奖池
	MSG_TYPE_OPEN_DAILY       byte = 5  //每日福利
	MSG_TYPE_OPEN_LUCKY       byte = 6  //内丹修炼
	MSG_TYPE_OPEN_RANK        byte = 7  //排行榜
	MSG_TYPE_OPEN_RANK_QUERY  byte = 8  //查询排行榜奖励情况
	MSG_TYPE_OPEN_ITEMS       byte = 9  //宝物开奖
	MSG_TYPE_OPEN_NEZHA       byte = 10 //哪吒救母
	MSG_TYPE_OPEN_NEIDAN      byte = 11 //内丹探索
	MSG_TYPE_OPEN_REROLL      byte = 12 //重抽
	MSG_TYPE_ELIXIR_DROP      byte = 20 //仙丹模式 -- 探索普通怪
	MSG_TYPE_ELIXIR_UNSEAL    byte = 21 //仙丹模式 -- 开鼎
	MSG_TYPE_ELIXIR_NEIDAN    byte = 22 //仙丹模式 -- 内丹探索

	COST_TYPE_100   int64 = 100
	COST_TYPE_1000  int64 = 1000
	COST_TYPE_10000 int64 = 10000
)
