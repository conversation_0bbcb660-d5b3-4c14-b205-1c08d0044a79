package unseal

import (
	"encoding/json"
	"fmt"
	"jackpot/api/core"
	"jackpot/filelog"
	"jackpot/items"
	"jackpot/net"
	"jackpot/users"
	"math/rand/v2"
	"os"
)

// Request 请求体
type Request struct {
	Oid    string  `json:"oid"`    // 批次ID
	UserId string  `json:"uid"`    // 用户ID，如果是全局开奖，此处传固定值 -1
	Map    int64   `json:"map"`    // 0-全部地图 100-火焰山，1000-盘丝洞，10000-狮驼岭
	Counts []int64 `json:"counts"` // 每种物品的数量，全地图时长度为24，否则为8
}

// Response 响应数据
type Response struct {
	Request
	Items   []items.Item `json:"items"`
	Elixirs []int        `json:"elixirs"` // 随机选择的仙丹索引（0-7）：单地图模式4个，全地图模式12个（3个地图各4个）
}

// ElixirResult 仙丹开奖结果
type ElixirResult struct {
	ElixirIndex int        // 仙丹索引 (0-7)
	Count       int        // 出现次数 (0-4)
	Gift        items.Item // 选中的礼物
	FinalValue  int64      // 最终价值 (gift.Value * gift.Count * countList[elixirIndex])
}

// ReplacementOption 替换选项
type ReplacementOption struct {
	OriginalElixir int
	NewResult      ElixirResult
	ValueReduction int64 // 替换后减少的价值
}

// CrossCountDowngradeOption 跨次数降档选项
type CrossCountDowngradeOption struct {
	ElixirIndex      int   // 要降档的仙丹索引
	OriginalCount    int   // 原始出现次数
	NewCount         int   // 降档后的出现次数
	ValueReduction   int64 // 降档减少的价值
	CompensationPos  int   // 补次数的位置（仙丹索引）
	CompensationCost int64 // 补次数的成本
}

// Handler 处理器
type Handler struct {
	logger  core.Logger
	giftMap map[int64]map[int][]items.Item // 修改为支持不同地图：map[mapId][count][]items.Item
}

// NewHandler 创建处理器
func NewHandler() *Handler {
	return &Handler{
		giftMap: make(map[int64]map[int][]items.Item),
	}
}

// Init 初始化处理器
func (h *Handler) Init(logger *filelog.FileLogger, config any) error {
	h.logger = core.NewLoggerAdapter(logger)

	// 读取仙丹开鼎配置文件
	jsonData, err := os.ReadFile("elixir_unseal.json")
	if err != nil {
		return err
	}

	var giftMapTemp = make(map[string]map[string][]items.ItemConfig)
	err = json.Unmarshal(jsonData, &giftMapTemp)
	if err != nil {
		return err
	}

	// 将配置转换为内部格式
	for mapKey, countMap := range giftMapTemp {
		var mapId int64
		fmt.Sscanf(mapKey, "%d", &mapId)

		if h.giftMap[mapId] == nil {
			h.giftMap[mapId] = make(map[int][]items.Item)
		}

		for countKey, itemConfigs := range countMap {
			var count int
			fmt.Sscanf(countKey, "%d", &count)

			var itemList []items.Item
			for _, itemConfig := range itemConfigs {
				itemList = append(itemList, items.Item(itemConfig))
			}
			h.giftMap[mapId][count] = itemList
		}
	}

	h.logger.Debug("elixir_unseal", "礼品地图配置: %+v", h.giftMap)
	return nil
}

// Validate 验证请求
func (h *Handler) Validate(req Request) error {
	if len(req.UserId) == 0 {
		return fmt.Errorf("无效用户参数")
	}

	if req.Map > 0 {
		switch req.Map {
		case net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000:
		default:
			return fmt.Errorf("无效地图参数")
		}

		if len(req.Counts) != 8 {
			return fmt.Errorf("无效或缺少counts参数")
		}
	} else if req.Map == 0 {
		if len(req.Counts) != 24 {
			return fmt.Errorf("无效或缺少counts参数")
		}
	} else {
		return fmt.Errorf("无效地图参数")
	}

	return nil
}

// Handle 处理请求
func (h *Handler) Handle(req Request) (int64, net.Response, error) {
	var jackpot int64
	var itemList []items.Item
	var allSelectedElixirs []int

	if req.Map > 0 {
		jackpot, itemList, allSelectedElixirs = h.open(req.Map, req.Counts)
	} else if req.Map == 0 {
		// 分割 req.Counts 为 3 个长度为 8 的切片
		var countSlices [3][]int64
		for i := range 3 {
			start := i * 8
			end := start + 8
			countSlices[i] = req.Counts[start:end]
		}

		// 定义一个包含3个地图枚举的数组
		mapId := [3]int64{net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000}

		// 定义一个空数组 results，数组类型是 items.Item
		results := make([]items.Item, 0)

		// 分别处理每个切片
		var jackpotResult int64
		for i, countSlice := range countSlices {
			var jp int64
			var items []items.Item
			var selectedElixirs []int
			// if req.UserId == "-1" && i > 1 {
			// 	jp, items = h.openGlobal(mapId[i], countSlice)
			// } else {
			// 	jp, items = h.open(mapId[i], countSlice)
			// }
			jp, items, selectedElixirs = h.open(mapId[i], countSlice)
			jackpotResult += jp
			results = append(results, items...)
			allSelectedElixirs = append(allSelectedElixirs, selectedElixirs...)
		}

		jackpot = jackpotResult
		itemList = results
	}

	responseData := &Response{
		Request: req,
		Items:   itemList,
	}

	// 返回开奖结果：单地图模式4个仙丹，全地图模式12个仙丹（3个地图各4个）
	if len(allSelectedElixirs) > 0 {
		responseData.Elixirs = allSelectedElixirs
	}

	response := net.Response{
		Code: 0,
		Msg:  "",
		Data: responseData,
	}

	return jackpot, response, nil
}

// open 开奖逻辑
func (h *Handler) open(mapId int64, countList []int64) (int64, []items.Item, []int) {
	// 获取全局奖池信息
	global := users.GetGlobalStats(mapId, users.VER_ELIXIR)

	h.logger.Debug("elixir_unseal", "[1] 开始开奖, 地图ID:%d, 数量列表:%+v, 全局奖池:%d", mapId, countList, global.TotalJackpot)

	// 步骤1: 随机选择4个仙丹（0-7，可重复）
	selectedElixirs := make([]int, 4)
	for i := 0; i < 4; i++ {
		selectedElixirs[i] = rand.IntN(8) // 0-7
	}

	h.logger.Debug("elixir_unseal", "[2] 选中的仙丹: %+v", selectedElixirs)

	// 步骤2: 统计每种仙丹的出现次数
	elixirCounts := make(map[int]int)
	for _, elixir := range selectedElixirs {
		elixirCounts[elixir]++
	}

	h.logger.Debug("elixir_unseal", "[3] 仙丹出现次数: %+v", elixirCounts)

	// 步骤3: 为每种出现的仙丹选择礼物
	selectedGifts := make(map[int]items.Item) // key: 仙丹索引, value: 选中的礼物
	for elixir, count := range elixirCounts {
		// 根据地图和出现次数从对应奖池中选择礼物
		mapGiftPool, exists := h.giftMap[mapId]
		if !exists {
			h.logger.Debug("elixir_unseal", "[4] 地图 %d 没有礼品池", mapId)
			continue
		}

		giftPool, exists := mapGiftPool[count]
		if !exists || len(giftPool) == 0 {
			h.logger.Debug("elixir_unseal", "[4] 地图 %d 出现次数 %d 没有礼品池", mapId, count)
			continue
		}

		selectedGift := items.SelectItem("elixir_unseal", giftPool)
		selectedGifts[elixir] = selectedGift

		h.logger.Debug("elixir_unseal", "[4] 仙丹 %d (出现次数 %d) 选中礼品: %s (价值:%d)", elixir, count, selectedGift.Name, selectedGift.Value*selectedGift.Count)
	}

	// 步骤4: 根据countList分配数量并计算总价值
	result := make([]items.Item, len(countList))
	totalValue := int64(0)

	for i, count := range countList {
		elixirIndex := i % 8 // 获取对应的仙丹索引

		// 检查该仙丹是否出现过
		if gift, exists := selectedGifts[elixirIndex]; exists {
			// 仙丹出现过，使用选中的礼物
			result[i] = items.Item{
				ID:    gift.ID,
				Name:  gift.Name,
				Value: gift.Value,
				Count: gift.Count,
				Total: count,
			}
			// 只有当count > 0时才计算价值
			if count > 0 {
				totalValue += gift.Value * gift.Count * count
			}
		} else if count > 0 {
			// 仙丹未出现且count > 0，使用出现次数0的奖池
			mapGiftPool, exists := h.giftMap[mapId]
			if exists {
				giftPool, exists := mapGiftPool[0]
				if exists && len(giftPool) > 0 {
					selectedGift := items.SelectItem("elixir_unseal", giftPool)
					result[i] = items.Item{
						ID:    selectedGift.ID,
						Name:  selectedGift.Name,
						Value: selectedGift.Value,
						Count: selectedGift.Count,
						Total: count,
					}
					totalValue += selectedGift.Value * selectedGift.Count * count

					h.logger.Debug("elixir_unseal", "[4.5] 仙丹 %d (出现次数 0) 选中礼品: %s (价值:%d)", elixirIndex, selectedGift.Name, selectedGift.Value*selectedGift.Count)
				}
			}
		}
		// 如果仙丹未出现且count <= 0，则result[i]保持为空的items.Item{}
	}

	h.logger.Debug("elixir_unseal", "[5] 奖池检查前总价值: %d, 全局奖池: %d", totalValue, global.TotalJackpot)

	// 步骤5: 检查全局奖池限制，必要时降档
	var finalElixirCounts map[int]int
	if totalValue > global.TotalJackpot {
		h.logger.Debug("elixir_unseal", "[6] 总价值超过奖池，需要降档")
		result, totalValue, finalElixirCounts = h.downgradeGifts(mapId, result, global.TotalJackpot, elixirCounts, countList)
	} else {
		finalElixirCounts = elixirCounts
	}

	// 步骤6: 根据最终的仙丹出现次数重新生成selectedElixirs
	finalSelectedElixirs := h.generateSelectedElixirsFromCounts(finalElixirCounts)
	h.logger.Debug("elixir_unseal", "[6.5] 最终选中的仙丹: %+v (基于最终出现次数: %+v)", finalSelectedElixirs, finalElixirCounts)

	// 更新全局奖池
	global.TotalReturn += totalValue
	global.TotalJackpot -= totalValue
	users.UpdateGobalStats(global)

	h.logger.Debug("elixir_unseal", "[7] 最终总价值: %d, 剩余奖池: %d", totalValue, global.TotalJackpot)

	return global.TotalJackpot, result, finalSelectedElixirs
}

// downgradeGifts 两步降档机制：先正常降档，再全结果重选
func (h *Handler) downgradeGifts(mapId int64, result []items.Item, maxJackpot int64, elixirCounts map[int]int, countList []int64) ([]items.Item, int64, map[int]int) {
	h.logger.Debug("elixir_unseal", "[6.1] 开始两步降档流程, 最大奖池: %d", maxJackpot)

	// 第一步：尝试正常降档（在同一出现次数内降档）
	normalResult, normalValue := h.normalDowngrade(mapId, result, maxJackpot, elixirCounts)

	// 检查正常降档是否成功
	if normalValue <= maxJackpot {
		h.logger.Debug("elixir_unseal", "[6.1] 正常降档成功, 最终价值: %d", normalValue)
		return normalResult, normalValue, elixirCounts
	}

	h.logger.Debug("elixir_unseal", "[6.1] 正常降档失败, 价值: %d, 尝试全局替换", normalValue)

	// 第二步：如果正常降档失败，尝试跨次数降档
	crossResult, crossValue, finalElixirCounts := h.crossCountDowngrade(mapId, normalResult, maxJackpot, elixirCounts, countList)

	// 检查跨次数降档是否成功
	if crossValue <= maxJackpot {
		h.logger.Debug("elixir_unseal", "[6.1] 跨次数降档成功, 最终价值: %d", crossValue)
		return crossResult, crossValue, finalElixirCounts
	}

	h.logger.Debug("elixir_unseal", "[6.1] 跨次数降档失败, 价值: %d, 返回目前开奖", crossValue)

	return crossResult, crossValue, finalElixirCounts
}

// normalDowngrade 正常降档：在同一出现次数内选择更低价值的礼物
func (h *Handler) normalDowngrade(mapId int64, result []items.Item, maxJackpot int64, elixirCounts map[int]int) ([]items.Item, int64) {
	h.logger.Debug("elixir_unseal", "[6.2] 开始正常降档流程")

	mapGiftPool, exists := h.giftMap[mapId]
	if !exists {
		h.logger.Debug("elixir_unseal", "[6.2.1] 地图 %d 没有礼品池", mapId)
		return result, 0
	}

	maxIterations := 10 // 防止无限循环
	iteration := 0

	for iteration < maxIterations {
		iteration++

		// 计算当前总价值
		currentTotalValue := int64(0)
		for _, item := range result {
			currentTotalValue += item.Value * item.Count * item.Total
		}

		// 检查是否已经满足奖池限制
		if currentTotalValue <= maxJackpot {
			h.logger.Debug("elixir_unseal", "[6.2.%d] 正常降档目标达成, 最终价值: %d", iteration, currentTotalValue)
			return result, currentTotalValue
		}

		needToSave := currentTotalValue - maxJackpot
		h.logger.Debug("elixir_unseal", "[6.2.%d] 需要节省: %d, 当前价值: %d", iteration, needToSave, currentTotalValue)

		// 创建降档选项列表
		type DowngradeOption struct {
			Index       int        // 结果数组中的索引
			ElixirIndex int        // 仙丹索引
			CurrentGift items.Item // 当前礼物
			NewGift     items.Item // 降档后的礼物
			SavedValue  int64      // 节省的价值
		}

		var options []DowngradeOption

		// 为每个有礼物的位置寻找降档选项（只在同一出现次数内）
		for i, item := range result {
			if item.Value == 0 || item.Total == 0 {
				continue
			}

			elixirIndex := i % 8

			// 确定该位置对应的出现次数
			var currentCount int
			if gift, exists := elixirCounts[elixirIndex]; exists {
				// 仙丹出现过，使用其出现次数
				currentCount = gift
			} else {
				// 仙丹未出现，使用出现次数0
				currentCount = 0
			}

			// 只在当前出现次数的奖池中寻找更低价值的礼物
			currentPool, exists := mapGiftPool[currentCount]
			if !exists || len(currentPool) == 0 {
				continue
			}

			// 寻找同一奖池中价值更低的礼物
			for _, lowerGift := range currentPool {
				if lowerGift.Value*lowerGift.Count < item.Value*item.Count {
					currentValue := item.Value * item.Count * item.Total
					newValue := lowerGift.Value * lowerGift.Count * item.Total
					savedValue := currentValue - newValue

					if savedValue > 0 {
						options = append(options, DowngradeOption{
							Index:       i,
							ElixirIndex: elixirIndex,
							CurrentGift: item,
							NewGift: items.Item{
								ID:    lowerGift.ID,
								Name:  lowerGift.Name,
								Value: lowerGift.Value,
								Count: lowerGift.Count,
								Total: item.Total,
							},
							SavedValue: savedValue,
						})
					}
				}
			}
		}

		if len(options) == 0 {
			h.logger.Debug("elixir_unseal", "[6.2.%d] 没有更多正常降档选项可用", iteration)
			break
		}

		h.logger.Debug("elixir_unseal", "[6.2.%d] 找到 %d 个正常降档选项", iteration, len(options))

		// 按节省价值从小到大排序，优先选择节省幅度最小的（更精确的降档）
		for i := 0; i < len(options)-1; i++ {
			for j := i + 1; j < len(options); j++ {
				if options[i].SavedValue > options[j].SavedValue {
					options[i], options[j] = options[j], options[i]
				}
			}
		}

		// 选择节省价值最小但足够的降档选项
		var selectedOption *DowngradeOption
		for _, option := range options {
			if option.SavedValue >= needToSave {
				selectedOption = &option
				break
			}
		}

		// 如果没有单个选项能满足需求，选择节省最多的
		if selectedOption == nil {
			// 重新按节省价值从大到小排序
			for i := 0; i < len(options)-1; i++ {
				for j := i + 1; j < len(options); j++ {
					if options[i].SavedValue < options[j].SavedValue {
						options[i], options[j] = options[j], options[i]
					}
				}
			}
			selectedOption = &options[0]
		}

		h.logger.Debug("elixir_unseal", "[6.2.%d] 应用正常降档在索引 %d: %s -> %s, 节省: %d",
			iteration, selectedOption.Index, selectedOption.CurrentGift.Name, selectedOption.NewGift.Name,
			selectedOption.SavedValue)

		result[selectedOption.Index] = selectedOption.NewGift
	}

	// 最终计算总价值
	finalTotalValue := int64(0)
	for _, item := range result {
		finalTotalValue += item.Value * item.Count * item.Total
	}

	h.logger.Debug("elixir_unseal", "[6.2.最终] 正常降档完成，经过 %d 次迭代, 最终价值: %d", iteration, finalTotalValue)

	return result, finalTotalValue
}

// calculateTotalValue 计算总价值
func (h *Handler) calculateTotalValue(result []items.Item) int64 {
	totalValue := int64(0)
	for _, item := range result {
		totalValue += item.Value * item.Count * item.Total
	}
	return totalValue
}

// crossCountDowngrade 跨次数降档：通过改变仙丹出现次数来降低总价值
func (h *Handler) crossCountDowngrade(mapId int64, result []items.Item, maxJackpot int64, elixirCounts map[int]int, countList []int64) ([]items.Item, int64, map[int]int) {
	h.logger.Debug("elixir_unseal", "[6.2.跨次数] 开始跨次数降档流程")

	_, exists := h.giftMap[mapId]
	if !exists {
		h.logger.Debug("elixir_unseal", "[6.2.跨次数] 地图 %d 没有礼品池", mapId)
		return result, h.calculateTotalValue(result), elixirCounts
	}

	maxIterations := 10 // 防止无限循环
	iteration := 0
	currentResult := make([]items.Item, len(result))
	copy(currentResult, result)
	currentElixirCounts := make(map[int]int)
	for k, v := range elixirCounts {
		currentElixirCounts[k] = v
	}

	for iteration < maxIterations {
		iteration++

		// 计算当前总价值
		currentTotalValue := h.calculateTotalValue(currentResult)

		// 检查是否已经满足奖池限制
		if currentTotalValue <= maxJackpot {
			h.logger.Debug("elixir_unseal", "[6.2.跨次数.%d] 跨次数降档目标达成, 最终价值: %d", iteration, currentTotalValue)
			return currentResult, currentTotalValue, currentElixirCounts
		}

		needToSave := currentTotalValue - maxJackpot
		h.logger.Debug("elixir_unseal", "[6.2.跨次数.%d] 需要节省: %d, 当前价值: %d", iteration, needToSave, currentTotalValue)

		// 找到可以跨次数降档的仙丹
		downgradeOption := h.findBestCrossCountDowngrade(mapId, currentResult, currentElixirCounts, countList)
		if downgradeOption == nil {
			h.logger.Debug("elixir_unseal", "[6.2.跨次数.%d] 没有找到可跨次数降档的选项", iteration)
			break
		}

		h.logger.Debug("elixir_unseal", "[6.2.跨次数.%d] 找到跨次数降档选项: 仙丹 %d 从次数 %d 降到 %d, 节省价值: %d",
			iteration, downgradeOption.ElixirIndex, downgradeOption.OriginalCount, downgradeOption.NewCount, downgradeOption.ValueReduction)

		// 应用跨次数降档
		newResult, newElixirCounts := h.applyCrossCountDowngrade(currentResult, currentElixirCounts, downgradeOption, mapId, countList)
		if newResult == nil {
			h.logger.Debug("elixir_unseal", "[6.2.跨次数.%d] 应用跨次数降档失败", iteration)
			break
		}

		currentResult = newResult
		currentElixirCounts = newElixirCounts
	}

	finalTotalValue := h.calculateTotalValue(currentResult)
	h.logger.Debug("elixir_unseal", "[6.2.跨次数.最终] 跨次数降档完成，经过 %d 次迭代, 最终价值: %d", iteration, finalTotalValue)

	return currentResult, finalTotalValue, currentElixirCounts
}

// findBestCrossCountDowngrade 找到最佳的跨次数降档选项
func (h *Handler) findBestCrossCountDowngrade(mapId int64, result []items.Item, elixirCounts map[int]int, countList []int64) *CrossCountDowngradeOption {
	_, exists := h.giftMap[mapId]
	if !exists {
		return nil
	}

	// 计算每个仙丹的当前价值
	elixirValues := make(map[int]int64)
	for elixirIndex := range elixirCounts {
		if elixirIndex < len(countList) && countList[elixirIndex] > 0 {
			// 获取当前仙丹的礼品价值
			for i, item := range result {
				if i%8 == elixirIndex && item.Value > 0 {
					elixirValues[elixirIndex] = item.Value * item.Count * countList[elixirIndex]
					break
				}
			}
		}
	}

	// 按价值从高到低排序仙丹
	type ElixirValuePair struct {
		ElixirIndex int
		Value       int64
		Count       int
	}

	var sortedElixirs []ElixirValuePair
	for elixirIndex, value := range elixirValues {
		if count, exists := elixirCounts[elixirIndex]; exists && count > 0 {
			sortedElixirs = append(sortedElixirs, ElixirValuePair{
				ElixirIndex: elixirIndex,
				Value:       value,
				Count:       count,
			})
		}
	}

	// 简单排序（按价值降序）
	for i := 0; i < len(sortedElixirs)-1; i++ {
		for j := i + 1; j < len(sortedElixirs); j++ {
			if sortedElixirs[i].Value < sortedElixirs[j].Value {
				sortedElixirs[i], sortedElixirs[j] = sortedElixirs[j], sortedElixirs[i]
			}
		}
	}

	h.logger.Debug("elixir_unseal", "[6.2.跨次数.查找] 仙丹价值排序: %+v", sortedElixirs)

	// 尝试每个仙丹的跨次数降档
	for _, elixirPair := range sortedElixirs {
		elixirIndex := elixirPair.ElixirIndex
		currentCount := elixirPair.Count

		// 尝试降档到更低的次数
		for newCount := currentCount - 1; newCount >= 0; newCount-- {
			// 计算降档后的价值
			newValue := h.calculateElixirValueAtCount(mapId, elixirIndex, newCount, countList)
			if newValue < 0 {
				continue // 无法获取该次数的礼品
			}

			valueReduction := elixirPair.Value - newValue
			h.logger.Debug("elixir_unseal", "[6.2.跨次数.查找] 仙丹 %d 从次数 %d 降到 %d, 价值从 %d 降到 %d, 减少 %d", elixirIndex, currentCount, newCount, elixirPair.Value, newValue, valueReduction)

			if valueReduction <= 0 {
				continue // 没有价值减少
			}

			// 寻找补次数的位置
			compensationPos, compensationCost := h.findBestCountCompensation(mapId, elixirIndex, elixirCounts, countList, currentCount-newCount)
			if compensationPos < 0 {
				h.logger.Debug("elixir_unseal", "[6.2.跨次数.查找] 仙丹 %d 无法找到补次数位置", elixirIndex)
				continue // 无法找到合适的补次数位置
			}

			// 计算净价值减少（降档减少的价值 - 补次数增加的成本）
			netReduction := valueReduction - compensationCost
			h.logger.Debug("elixir_unseal", "[6.2.跨次数.查找] 仙丹 %d 净减少价值: %d (降档减少: %d, 补次数成本: %d)", elixirIndex, netReduction, valueReduction, compensationCost)

			if netReduction < 0 {
				break
			}

			// 找到有效的跨次数降档选项
			return &CrossCountDowngradeOption{
				ElixirIndex:      elixirIndex,
				OriginalCount:    currentCount,
				NewCount:         newCount,
				ValueReduction:   netReduction,
				CompensationPos:  compensationPos,
				CompensationCost: compensationCost,
			}
		}
	}

	return nil // 没有找到合适的跨次数降档选项
}

// calculateElixirValueAtCount 计算指定仙丹在指定次数下的最低价值
func (h *Handler) calculateElixirValueAtCount(mapId int64, elixirIndex int, count int, countList []int64) int64 {
	mapGiftPool, exists := h.giftMap[mapId]
	if !exists {
		return -1
	}

	giftPool, exists := mapGiftPool[count]
	if !exists || len(giftPool) == 0 {
		return -1
	}

	if elixirIndex >= len(countList) { // || countList[elixirIndex] <= 0
		return -1
	}

	// 找到该次数奖池中价值最低的礼品
	minValue := int64(-1)
	for _, gift := range giftPool {
		value := gift.Value * gift.Count * countList[elixirIndex]
		if minValue < 0 || value < minValue {
			minValue = value
		}
	}

	return minValue
}

// findBestCountCompensation 找到最佳的补次数位置
func (h *Handler) findBestCountCompensation(mapId int64, orgElixirIndex int, elixirCounts map[int]int, countList []int64, needCompensation int) (int, int64) {
	_, exists := h.giftMap[mapId]
	if !exists {
		return -1, 0
	}

	bestPos := -1
	bestCost := int64(-1)

	// 遍历所有仙丹位置，寻找最佳补次数位置
	for elixirIndex := 0; elixirIndex < 8; elixirIndex++ {
		if elixirIndex == orgElixirIndex { //|| elixirIndex >= len(countList)
			continue
		}

		// 检查该仙丹当前的出现次数
		currentCount := 0
		if count, exists := elixirCounts[elixirIndex]; exists {
			currentCount = count
		}

		// 只考虑当前次数比需要补偿次数少的仙丹
		// if currentCount >= needCompensation {
		// 	continue
		// }

		// 计算补次数后的成本
		newCount := currentCount + needCompensation
		if newCount > 4 {
			continue // 次数不能超过4
		}

		// 获取该次数的最低价值礼品
		minValue := h.calculateElixirValueAtCount(mapId, elixirIndex, newCount, countList)
		if minValue < 0 {
			continue
		}

		// 计算当前价值（如果有的话）
		currentValue := int64(0)
		// if currentCount > 0 {
		currentValue = h.calculateElixirValueAtCount(mapId, elixirIndex, currentCount, countList)
		if currentValue < 0 {
			currentValue = 0
		}
		// }

		// 计算补次数的成本（新价值 - 当前价值）
		cost := minValue - currentValue

		h.logger.Debug("elixir_unseal", "[6.2.跨次数.补次数] 仙丹 %d 从次数 %d 补到 %d, 成本: %d (新价值: %d, 当前价值: %d)",
			elixirIndex, currentCount, newCount, cost, minValue, currentValue)

		// 选择成本最低的位置
		if bestPos < 0 || cost < bestCost {
			bestPos = elixirIndex
			bestCost = cost
		}
	}

	return bestPos, bestCost
}

// applyCrossCountDowngrade 应用跨次数降档
func (h *Handler) applyCrossCountDowngrade(result []items.Item, elixirCounts map[int]int, option *CrossCountDowngradeOption, mapId int64, countList []int64) ([]items.Item, map[int]int) {
	mapGiftPool, exists := h.giftMap[mapId]
	if !exists {
		return nil, nil
	}

	// 复制结果数组和仙丹计数
	newResult := make([]items.Item, len(result))
	copy(newResult, result)
	newElixirCounts := make(map[int]int)
	for k, v := range elixirCounts {
		newElixirCounts[k] = v
	}

	// 1. 降档目标仙丹
	targetElixir := option.ElixirIndex
	newCount := option.NewCount

	// 获取新次数的礼品池
	giftPool, exists := mapGiftPool[newCount]
	if !exists || len(giftPool) == 0 {
		h.logger.Debug("elixir_unseal", "[6.2.跨次数.应用] 仙丹 %d 次数 %d 没有礼品池", targetElixir, newCount)
		return nil, nil
	}

	// 选择最低价值的礼品
	var selectedGift items.Item
	minValue := int64(-1)
	for _, gift := range giftPool {
		value := gift.Value * gift.Count
		if minValue < 0 || value < minValue {
			minValue = value
			selectedGift = gift
		}
	}

	// 更新目标仙丹的结果
	for i := range newResult {
		if i%8 == targetElixir {
			// 无论countList是否为0，都要设置仙丹结果（因为仙丹已经被选中）
			newResult[i] = items.Item{
				ID:    selectedGift.ID,
				Name:  selectedGift.Name,
				Value: selectedGift.Value,
				Count: selectedGift.Count,
				Total: countList[targetElixir], // 即使为0也要设置
			}
		}
	}

	// 更新仙丹计数
	if newCount > 0 {
		newElixirCounts[targetElixir] = newCount
	} else {
		delete(newElixirCounts, targetElixir)
	}

	h.logger.Debug("elixir_unseal", "[6.2.跨次数.应用] 仙丹 %d 降档到次数 %d, 选择礼品: %s (价值:%d)",
		targetElixir, newCount, selectedGift.Name, selectedGift.Value*selectedGift.Count)

	// 2. 补次数到指定位置
	compensationPos := option.CompensationPos
	compensationCount := option.OriginalCount - newCount

	// 获取补次数位置的当前次数
	currentCompensationCount := 0
	if count, exists := newElixirCounts[compensationPos]; exists {
		currentCompensationCount = count
	}

	finalCompensationCount := currentCompensationCount + compensationCount
	if finalCompensationCount > 4 {
		h.logger.Debug("elixir_unseal", "[6.2.跨次数.应用] 补次数位置 %d 次数超过限制: %d", compensationPos, finalCompensationCount)
		return nil, nil
	}

	// 获取补次数位置的礼品池
	compensationGiftPool, exists := mapGiftPool[finalCompensationCount]
	if !exists || len(compensationGiftPool) == 0 {
		h.logger.Debug("elixir_unseal", "[6.2.跨次数.应用] 补次数位置 %d 次数 %d 没有礼品池", compensationPos, finalCompensationCount)
		return nil, nil
	}

	// 选择最低价值的礼品
	var compensationGift items.Item
	minCompensationValue := int64(-1)
	for _, gift := range compensationGiftPool {
		value := gift.Value * gift.Count
		if minCompensationValue < 0 || value < minCompensationValue {
			minCompensationValue = value
			compensationGift = gift
		}
	}

	// 更新补次数位置的结果
	for i := range newResult {
		if i%8 == compensationPos {
			if countList[compensationPos] > 0 {
				newResult[i] = items.Item{
					ID:    compensationGift.ID,
					Name:  compensationGift.Name,
					Value: compensationGift.Value,
					Count: compensationGift.Count,
					Total: countList[compensationPos],
				}
			}
		}
	}

	// 更新补次数位置的仙丹计数
	newElixirCounts[compensationPos] = finalCompensationCount

	h.logger.Debug("elixir_unseal", "[6.2.跨次数.应用] 仙丹 %d 补次数到 %d, 选择礼品: %s (价值:%d)",
		compensationPos, finalCompensationCount, compensationGift.Name, compensationGift.Value*compensationGift.Count)

	return newResult, newElixirCounts
}

// generateSelectedElixirsFromCounts 根据仙丹出现次数生成selectedElixirs数组
func (h *Handler) generateSelectedElixirsFromCounts(elixirCounts map[int]int) []int {
	var selectedElixirs []int

	// 根据每个仙丹的出现次数，将其添加到结果数组中
	for elixirIndex, count := range elixirCounts {
		for i := 0; i < count; i++ {
			selectedElixirs = append(selectedElixirs, elixirIndex)
		}
	}

	// 如果总数不足4个，用随机仙丹补齐（这种情况理论上不应该发生，但为了安全起见）
	for len(selectedElixirs) < 4 {
		selectedElixirs = append(selectedElixirs, rand.IntN(8))
	}

	// 如果超过4个，截取前4个（这种情况也不应该发生）
	if len(selectedElixirs) > 4 {
		selectedElixirs = selectedElixirs[:4]
	}

	return selectedElixirs
}

// NewModule 创建模块
func NewModule() core.APIModule {
	handler := NewHandler()

	return core.NewModuleBuilder[Request, net.Response]("elixir_unseal", "v1", net.MSG_TYPE_ELIXIR_UNSEAL).
		WithHandler(handler).
		Build()
}
