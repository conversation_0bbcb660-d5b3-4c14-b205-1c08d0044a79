package exchange

import (
	"encoding/json"
	"fmt"
	"jackpot/api/core"
	"jackpot/filelog"
	"jackpot/items"
	"jackpot/net"
	"jackpot/users"
	"os"
	"slices"
)

// Request 请求体
type Request struct {
	Oid    string  `json:"oid"`    // 兑换批次ID
	UserId string  `json:"uid"`    // 用户ID，如果是全局开奖，此处传固定值 -1
	Map    int64   `json:"map"`    // 0-全部地图 100-火焰山，1000-盘丝洞，10000-狮驼岭
	Counts []int64 `json:"counts"` // 每种物品的数量，全地图时长度为24，否则为8
}

// Response 响应数据
type Response struct {
	Request
	Items []items.Item `json:"items"`
}

// Handler 兑换处理器
type Handler struct {
	logger  core.Logger
	itemMap map[int64][]items.Item
	giftMap map[int][]items.Item
}

// NewHandler 创建兑换处理器
func NewHandler() *Handler {
	return &Handler{
		itemMap: make(map[int64][]items.Item),
		giftMap: make(map[int][]items.Item),
	}
}

// Init 初始化处理器
func (h *Handler) Init(logger *filelog.FileLogger, config any) error {
	h.logger = core.NewLoggerAdapter(logger)

	if err := h.readItemJson(); err != nil {
		return err
	}

	if err := h.readGiftJson(); err != nil {
		return err
	}

	h.logger.Debug("exchange", "giftMap: %+v", h.giftMap)
	return nil
}

// readGiftJson 读取礼物配置
func (h *Handler) readGiftJson() error {
	jsonData, err := os.ReadFile("exchange.json")
	if err != nil {
		return err
	}

	var itemMapTemp = make(map[string][]items.ItemConfig)
	err = json.Unmarshal(jsonData, &itemMapTemp)
	if err != nil {
		return err
	}

	for k, v := range itemMapTemp {
		var key int
		fmt.Sscanf(k, "%d", &key)
		var itemList []items.Item
		for _, itemConfig := range v {
			itemList = append(itemList, items.Item(itemConfig))
		}
		h.giftMap[key] = itemList
	}
	return nil
}

// readItemJson 读取物品配置
func (h *Handler) readItemJson() error {
	jsonData, err := os.ReadFile("monster.json")
	if err != nil {
		return err
	}

	var itemMapTemp = make(map[string][]items.ItemConfig)
	err = json.Unmarshal(jsonData, &itemMapTemp)
	if err != nil {
		return err
	}

	for k, v := range itemMapTemp {
		var key int64
		fmt.Sscanf(k, "%d", &key)
		var itemList []items.Item
		for _, itemConfig := range v {
			itemList = append(itemList, items.Item(itemConfig))
		}
		h.itemMap[key] = itemList
	}

	return nil
}

// Validate 验证请求
func (h *Handler) Validate(req Request) error {
	if len(req.UserId) == 0 {
		return fmt.Errorf("无效用户参数")
	}

	if req.Map > 0 {
		switch req.Map {
		case net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000:
		default:
			return fmt.Errorf("无效地图参数")
		}

		if len(req.Counts) != 8 {
			return fmt.Errorf("无效或缺少counts参数")
		}
	} else if req.Map == 0 {
		if len(req.Counts) != 24 {
			return fmt.Errorf("无效或缺少counts参数")
		}
	} else {
		return fmt.Errorf("无效地图参数")
	}

	return nil
}

// Handle 处理请求
func (h *Handler) Handle(req Request) (int64, net.Response, error) {
	var jackpot int64
	var itemList []items.Item

	if req.Map > 0 {
		jackpot, itemList = h.open(req.Map, req.Counts)
	} else if req.Map == 0 {
		// 分割 req.Counts 为 3 个长度为 8 的切片
		var countSlices [3][]int64
		for i := range 3 {
			start := i * 8
			end := start + 8
			countSlices[i] = req.Counts[start:end]
		}

		// 定义一个包含3个地图枚举的数组
		mapId := [3]int64{net.COST_TYPE_100, net.COST_TYPE_1000, net.COST_TYPE_10000}

		// 定义一个空数组 results，数组类型是 items.Item
		results := make([]items.Item, 0)

		// 分别处理每个切片
		var jackpotResult int64
		for i, countSlice := range countSlices {
			var jp int64
			var items []items.Item
			// if req.UserId == "-1" && i > 1 {
			// 	jp, items = h.openGlobal(mapId[i], countSlice)
			// } else {
			// 	jp, items = h.open(mapId[i], countSlice)
			// }
			jp, items = h.open(mapId[i], countSlice)
			jackpotResult += jp
			results = append(results, items...)
		}

		jackpot = jackpotResult
		itemList = results
	}

	response := net.Response{
		Code: 0,
		Msg:  "",
		Data: &Response{
			Request: req,
			Items:   itemList,
		},
	}

	return jackpot, response, nil
}

// open 开奖逻辑
func (h *Handler) open(mapId int64, countList []int64) (int64, []items.Item) {
	global := users.GetGlobalStats(mapId, users.VER_V2)
	itemList := h.itemMap[mapId]
	result := make([]items.Item, len(itemList))
	totalReturn := int64(0)
	totalJackpot := global.TotalJackpot

	for i := len(itemList) - 1; i >= 0; i-- {
		item := itemList[i]
		count := countList[i]

		giftList := append(make([]items.Item, 0, len(h.giftMap[item.ID])), h.giftMap[item.ID]...)

		for j := len(giftList) - 1; j >= 0; j-- {
			gift := giftList[j]
			giftCost := gift.Value * gift.Count * count
			if count > 0 && giftCost > totalJackpot && len(giftList) > 1 {
				giftList = slices.Delete(giftList, j, j+1)
			}
		}

		giftList = items.Normalize(giftList)

		selectedItem := items.SelectItemSortByValue(giftList)
		selectedItem.Total = count

		total := selectedItem.Value * selectedItem.Count * selectedItem.Total
		totalReturn += total
		totalJackpot -= total

		result[i] = selectedItem
		h.logger.Debug("exchange", "[10] return item:%s, value:%d, count:%d, total:%d", selectedItem.Name, selectedItem.Value, selectedItem.Count, selectedItem.Total)
	}

	global.TotalReturn += totalReturn
	global.TotalJackpot -= totalReturn
	users.UpdateGobalStats(global)

	return global.TotalJackpot, result
}

// openGlobal 全局开奖逻辑
func (h *Handler) openGlobal(mapId int64, countList []int64) (int64, []items.Item) {
	global := users.GetGlobalStats(mapId, users.VER_V2)
	itemList := h.itemMap[mapId]
	result := make([]items.Item, len(itemList))
	totalReturn := int64(0)
	remainingJackpot := global.TotalJackpot

	type GiftOption struct {
		Index     int
		Count     int64
		Gift      items.Item
		TotalCost int64
	}

	allOptions := make([]GiftOption, 0)

	for i, item := range itemList {
		count := countList[i]
		giftList := append(make([]items.Item, 0, len(h.giftMap[item.ID])), h.giftMap[item.ID]...)

		if count <= 0 {
			selectedItem := items.SelectItemSortByValue(giftList)
			selectedItem.Total = 0
			result[i] = selectedItem
			continue
		}

		lowestGift := giftList[0]
		lowestCost := lowestGift.Value * lowestGift.Count * count

		result[i] = items.Item{
			ID:    lowestGift.ID,
			Name:  lowestGift.Name,
			Value: lowestGift.Value,
			Count: lowestGift.Count,
			Total: count,
		}

		totalReturn += lowestCost
		remainingJackpot -= lowestCost

		for j := 1; j < len(giftList); j++ {
			gift := giftList[j]
			totalCost := gift.Value * gift.Count * count

			allOptions = append(allOptions, GiftOption{
				Index:     i,
				Count:     count,
				Gift:      gift,
				TotalCost: totalCost,
			})
		}
	}

	if remainingJackpot <= 0 {
		global.TotalReturn += totalReturn
		global.TotalJackpot -= totalReturn
		users.UpdateGobalStats(global)
		return global.TotalJackpot, result
	}

	slices.SortFunc(allOptions, func(a, b GiftOption) int {
		if a.TotalCost < b.TotalCost {
			return -1
		} else if a.TotalCost > b.TotalCost {
			return 1
		}
		return 0
	})

	for _, option := range allOptions {
		currentItem := result[option.Index]
		currentCost := currentItem.Value * currentItem.Count * currentItem.Total
		netIncrease := option.TotalCost - currentCost

		if netIncrease > remainingJackpot {
			break
		}

		result[option.Index] = items.Item{
			ID:    option.Gift.ID,
			Name:  option.Gift.Name,
			Value: option.Gift.Value,
			Count: option.Gift.Count,
			Total: option.Count,
		}

		totalReturn += netIncrease
		remainingJackpot -= netIncrease
	}

	global.TotalReturn += totalReturn
	global.TotalJackpot -= totalReturn
	users.UpdateGobalStats(global)

	return global.TotalJackpot, result
}

// NewModule 创建兑换模块
func NewModule() core.APIModule {
	handler := NewHandler()

	return core.NewModuleBuilder[Request, net.Response]("exchange", "v1", net.MSG_TYPE_OPEN_ITEMS).
		WithHandler(handler).
		Build()
}
